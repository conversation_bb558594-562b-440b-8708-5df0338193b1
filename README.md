# 葵 (kui)

一个基于终端的 Kubernetes 资源管理 TUI 工具。

## 特性

- 🚀 **轻量级**: 单一可执行文件，无外部依赖
- 🎯 **直观操作**: vim 风格的键盘快捷键
- 📊 **实时监控**: 自动刷新资源状态
- 🔄 **多集群支持**: 支持 kubeconfig 多上下文切换
- 🎨 **美观界面**: 基于 Bubbletea 和 Lipgloss 的现代 TUI 设计，四栏布局（header、sidebar、content、footer）

## 安装

### 从源码构建

```bash
git clone https://github.com/liyujun-dev/kui.git
cd kui
make build
```

### 使用

```bash
# 启动 TUI 界面（默认行为）
./kui

# 显示帮助信息
./kui --help
./kui help

# 显示版本信息
./kui --version
./kui -v
./kui version
```

## 功能

### 资源浏览
- **Pods**: 查看 Pod 状态、重启次数、节点信息
- **Services**: 查看服务类型、端口、选择器
- **Deployments**: 查看副本状态、更新策略
- **Nodes**: 查看节点状态、版本、角色
- **Namespaces**: 查看命名空间状态

### 键盘快捷键
- `1-5`: 切换资源类型 (Pods, Services, Deployments, Nodes, Namespaces)
- `j/k` 或 `↓/↑`: 上下移动选择
- `r`: 刷新当前资源列表
- `q` 或 `Ctrl+C`: 退出应用

## 配置

kui 使用标准的 kubeconfig 配置：

```bash
# 默认配置文件位置
~/.kube/config

# 或通过环境变量指定
export KUBECONFIG=/path/to/your/kubeconfig
```

## 开发

### 项目结构

```
kui/
├── cmd/                 # CLI 命令定义
├── internal/
│   ├── config/         # 配置管理
│   ├── k8s/           # Kubernetes 客户端
│   ├── models/        # 数据模型
│   └── tui/           # TUI 界面
├── Makefile           # 构建脚本
└── main.go           # 程序入口
```

### 构建命令

```bash
make build      # 构建
make test       # 测试
make clean      # 清理
make tui        # 构建并运行 TUI
make build-all  # 交叉编译所有平台
```

## 技术栈

- **Go**: 主要编程语言
- **Cobra**: CLI 框架
- **Bubbletea**: TUI 框架
- **client-go**: Kubernetes 客户端库

## 许可证

MIT License

