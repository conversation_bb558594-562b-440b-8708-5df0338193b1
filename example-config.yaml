# kui Application Configuration Example
# This config file only contains kui application UI and behavior settings
# Use standard kubeconfig file for Kubernetes cluster configuration

# Theme configuration (simplified - removed color_scheme and variant)
theme:
  # Color configuration (uses Catppuccin Mocha by default)
  colors:
    # Primary colors
    primary: "#cba6f7" # Primary color (Mauve)
    secondary: "#89b4fa" # Secondary color (Blue)
    success: "#a6e3a1" # Success status color (Green)
    warning: "#f9e2af" # Warning status color (Yellow)
    error: "#f38ba8" # Error status color (Red)
    info: "#89dceb" # Info status color (Sky)

    # Neutral colors
    background: "#1e1e2e" # Background color (Base)
    surface: "#313244" # Surface color (Surface0)
    border: "#585b70" # Border color (Surface2)
    text: "#cdd6f4" # Text color (Text)
    text_muted: "#a6adc8" # Muted text color (Subtext0)
    text_dim: "#9399b2" # Dim text color (Overlay2)

    # Status colors
    status_running: "#a6e3a1" # Running status color
    status_pending: "#f9e2af" # Pending status color
    status_failed: "#f38ba8" # Failed status color
    status_terminating: "#fab387" # Terminating status color
    status_unknown: "#a6adc8" # Unknown status color

    # Selection colors
    selected: "#45475a" # Selected color (Surface1)
    highlighted: "#585b70" # Highlighted color (Surface2)

# 快捷键配置
keybindings:
  quit: ["q", "ctrl+c"] # 退出应用
  refresh: ["r", "f5"] # 刷新资源
  navigate: ["j", "k", "up", "down"] # 导航移动
  select_pod: "1" # 选择Pod资源
  select_service: "2" # 选择Service资源
  select_deployment: "3" # 选择Deployment资源
  select_node: "4" # 选择Node资源
  select_namespace: "5" # 选择Namespace资源

# 布局配置
layout:
  sidebar_width: 20 # 侧边栏宽度
  show_icons: true # 显示图标
  show_status_icons: true # 显示状态图标
  compact_mode: false # 紧凑模式

# 通用配置
general:
  refresh_interval: 5 # 自动刷新间隔（秒）
  language: "en" # 界面语言 (en, zh)
  log_level: "info" # 日志级别 (debug, info, warn, error)

# 注意：
# 1. Kubernetes集群配置请使用 ~/.kube/config 文件
# 2. 或通过 KUBECONFIG 环境变量指定配置文件路径
# 3. kui会自动读取当前kubectl上下文和命名空间设置
