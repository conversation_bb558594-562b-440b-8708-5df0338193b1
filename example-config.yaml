# kui 应用程序配置示例
# 此配置文件只包含kui应用程序的UI和行为设置
# Kubernetes集群配置请使用标准的kubeconfig文件

# 主题配置
theme:
  color_scheme: "catppuccin"  # 颜色方案
  variant: "mocha"            # 变体: mocha, latte, frappe, macchiato
  
  # 自定义颜色配置（可选，留空则使用预设主题）
  colors:
    # 主要颜色
    primary: "#cba6f7"    # 主色调 (Mauve)
    secondary: "#89b4fa"  # 次要色调 (Blue)
    success: "#a6e3a1"    # 成功状态色 (Green)
    warning: "#f9e2af"    # 警告状态色 (Yellow)
    error: "#f38ba8"      # 错误状态色 (Red)
    info: "#89dceb"       # 信息状态色 (Sky)
    
    # 中性颜色
    background: "#1e1e2e" # 背景色 (Base)
    surface: "#313244"    # 表面色 (Surface0)
    border: "#585b70"     # 边框色 (Surface2)
    text: "#cdd6f4"       # 文本色 (Text)
    text_muted: "#a6adc8" # 次要文本色 (Subtext0)
    text_dim: "#9399b2"   # 暗淡文本色 (Overlay2)
    
    # 状态颜色
    status_running: "#a6e3a1"     # 运行状态色
    status_pending: "#f9e2af"     # 等待状态色
    status_failed: "#f38ba8"      # 失败状态色
    status_terminating: "#fab387" # 终止状态色
    status_unknown: "#a6adc8"     # 未知状态色
    
    # 选择颜色
    selected: "#45475a"    # 选中色 (Surface1)
    highlighted: "#585b70" # 高亮色 (Surface2)

# 快捷键配置
keybindings:
  quit: ["q", "ctrl+c"]           # 退出应用
  refresh: ["r", "f5"]            # 刷新资源
  navigate: ["j", "k", "up", "down"] # 导航移动
  select_pod: "1"                 # 选择Pod资源
  select_service: "2"             # 选择Service资源
  select_deployment: "3"          # 选择Deployment资源
  select_node: "4"                # 选择Node资源
  select_namespace: "5"           # 选择Namespace资源

# 布局配置
layout:
  sidebar_width: 20      # 侧边栏宽度
  show_icons: true       # 显示图标
  show_status_icons: true # 显示状态图标
  compact_mode: false    # 紧凑模式

# 通用配置
general:
  refresh_interval: 5    # 自动刷新间隔（秒）
  language: "en"         # 界面语言 (en, zh)
  log_level: "info"      # 日志级别 (debug, info, warn, error)

# 注意：
# 1. Kubernetes集群配置请使用 ~/.kube/config 文件
# 2. 或通过 KUBECONFIG 环境变量指定配置文件路径
# 3. kui会自动读取当前kubectl上下文和命名空间设置
