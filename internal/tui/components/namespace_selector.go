package components

import (
	"fmt"

	"github.com/charmbracelet/lipgloss"
)

// NamespaceSelector namespace selector component
type NamespaceSelector struct {
	currentNamespace string
	width            int
}

// NewNamespaceSelector creates a new namespace selector
func NewNamespaceSelector(currentNamespace string, width int) *NamespaceSelector {
	return &NamespaceSelector{
		currentNamespace: currentNamespace,
		width:            width,
	}
}

// SetNamespace sets the current namespace
func (ns *NamespaceSelector) SetNamespace(namespace string) *NamespaceSelector {
	ns.currentNamespace = namespace
	return ns
}

// SetWidth sets the width
func (ns *NamespaceSelector) SetWidth(width int) *NamespaceSelector {
	ns.width = width
	return ns
}

// <PERSON><PERSON> renders the namespace selector matching UI design
func (ns *NamespaceSelector) Render() string {
	// Create namespace info style (simple text, no border) - Latte colors
	selectorStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("#6c6f85")). // Latte Subtext0
		Padding(0, 1)

	// Create content without dropdown arrow, just namespace info
	content := fmt.Sprintf("Namespace: %s", ns.currentNamespace)

	return selectorStyle.Render(content)
}

// NamespaceSelectorPanel creates a namespace selector panel
func NamespaceSelectorPanel(currentNamespace string, width int) *NamespaceSelector {
	return NewNamespaceSelector(currentNamespace, width)
}
