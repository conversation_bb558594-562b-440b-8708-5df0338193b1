package components

import (
	"fmt"

	"github.com/charmbracelet/lipgloss"
)

// NamespaceSelector namespace selector component
type NamespaceSelector struct {
	currentNamespace string
	width            int
}

// NewNamespaceSelector creates a new namespace selector
func NewNamespaceSelector(currentNamespace string, width int) *NamespaceSelector {
	return &NamespaceSelector{
		currentNamespace: currentNamespace,
		width:            width,
	}
}

// SetNamespace sets the current namespace
func (ns *NamespaceSelector) SetNamespace(namespace string) *NamespaceSelector {
	ns.currentNamespace = namespace
	return ns
}

// SetWidth sets the width
func (ns *NamespaceSelector) SetWidth(width int) *NamespaceSelector {
	ns.width = width
	return ns
}

// <PERSON><PERSON> renders the namespace selector
func (ns *NamespaceSelector) Render() string {
	// Create namespace selector style
	selectorStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("#585b70")). // Border color
		Background(lipgloss.Color("#313244")).       // Surface color
		Foreground(lipgloss.Color("#cdd6f4")).       // Text color
		Padding(0, 1).
		Width(ns.width - 4) // Account for border and padding

	// Create content with dropdown arrow
	content := fmt.Sprintf("Namespace: %s ▼", ns.currentNamespace)
	
	return selectorStyle.Render(content)
}

// NamespaceSelectorPanel creates a namespace selector panel
func NamespaceSelectorPanel(currentNamespace string, width int) *NamespaceSelector {
	return NewNamespaceSelector(currentNamespace, width)
}
