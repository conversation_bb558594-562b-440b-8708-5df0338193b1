package components

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/tui/styles"
)

// ListItem 列表项接口
type ListItem interface {
	Render(selected bool, width int) string
	GetID() string
}

// SimpleListItem 简单列表项
type SimpleListItem struct {
	ID      string
	Content string
	Icon    string
}

// Render 渲染简单列表项
func (item *SimpleListItem) Render(selected bool, width int) string {
	var style lipgloss.Style
	if selected {
		style = styles.SidebarSelectedStyle.Width(width - 4)
	} else {
		style = styles.SidebarItemStyle.Width(width - 4)
	}

	content := item.Content
	if item.Icon != "" {
		if selected {
			content = fmt.Sprintf("▶ %s %s", item.Icon, item.Content)
		} else {
			content = fmt.Sprintf("  %s %s", item.Icon, item.Content)
		}
	}

	return style.Render(content)
}

// GetID 获取列表项ID
func (item *SimpleListItem) GetID() string {
	return item.ID
}

// TableListItem 表格列表项
type TableListItem struct {
	ID      string
	Columns []string
}

// Render 渲染表格列表项
func (item *TableListItem) Render(selected bool, width int) string {
	var style lipgloss.Style
	if selected {
		style = styles.ContentSelectedRowStyle
	} else {
		style = styles.ContentRowStyle
	}

	content := strings.Join(item.Columns, " ")
	return style.Render(content)
}

// GetID 获取表格列表项ID
func (item *TableListItem) GetID() string {
	return item.ID
}

// List 列表组件
type List struct {
	items    []ListItem
	selected int
	width    int
	height   int
	header   string
}

// NewList 创建新的列表
func NewList() *List {
	return &List{
		items:    make([]ListItem, 0),
		selected: 0,
	}
}

// SetItems 设置列表项
func (l *List) SetItems(items []ListItem) *List {
	l.items = items
	if l.selected >= len(items) {
		l.selected = len(items) - 1
	}
	if l.selected < 0 {
		l.selected = 0
	}
	return l
}

// SetSize 设置列表大小
func (l *List) SetSize(width, height int) *List {
	l.width = width
	l.height = height
	return l
}

// SetHeader 设置列表头部
func (l *List) SetHeader(header string) *List {
	l.header = header
	return l
}

// SetSelected 设置选中项
func (l *List) SetSelected(index int) *List {
	if index >= 0 && index < len(l.items) {
		l.selected = index
	}
	return l
}

// GetSelected 获取选中项索引
func (l *List) GetSelected() int {
	return l.selected
}

// GetSelectedItem 获取选中的列表项
func (l *List) GetSelectedItem() ListItem {
	if l.selected >= 0 && l.selected < len(l.items) {
		return l.items[l.selected]
	}
	return nil
}

// MoveUp 向上移动选择
func (l *List) MoveUp() {
	if l.selected > 0 {
		l.selected--
	}
}

// MoveDown 向下移动选择
func (l *List) MoveDown() {
	if l.selected < len(l.items)-1 {
		l.selected++
	}
}

// Render 渲染列表
func (l *List) Render() string {
	if len(l.items) == 0 {
		emptyContent := styles.EmptyStyle.Render("No items")
		return lipgloss.NewStyle().
			Width(l.width).
			Height(l.height).
			Padding(1, 1).
			Render(emptyContent)
	}

	var lines []string

	// 添加头部
	if l.header != "" {
		headerStyle := styles.ContentHeaderStyle
		lines = append(lines, headerStyle.Render(l.header))
	}

	// 渲染列表项
	for i, item := range l.items {
		selected := i == l.selected
		line := item.Render(selected, l.width)
		lines = append(lines, line)
	}

	// 填充剩余空间
	contentHeight := l.height
	if l.header != "" {
		contentHeight-- // 减去头部高度
	}
	
	for len(lines) < contentHeight {
		lines = append(lines, "")
	}

	content := strings.Join(lines, "\n")
	return lipgloss.NewStyle().
		Width(l.width).
		Height(l.height).
		Render(content)
}

// SidebarList 创建侧边栏列表
func SidebarList(items []ListItem, selected int, width, height int) *List {
	return NewList().
		SetItems(items).
		SetSelected(selected).
		SetSize(width, height)
}

// TableList 创建表格列表
func TableList(header string, items []ListItem, selected int, width, height int) *List {
	return NewList().
		SetHeader(header).
		SetItems(items).
		SetSelected(selected).
		SetSize(width, height)
}

// ResourceListItem 资源列表项
type ResourceListItem struct {
	ID       string
	Name     string
	Status   string
	Columns  []string
	Selected bool
}

// Render 渲染资源列表项
func (item *ResourceListItem) Render(selected bool, width int) string {
	var style lipgloss.Style
	if selected {
		style = styles.ContentSelectedRowStyle
	} else {
		style = styles.ContentRowStyle
	}

	// 格式化列内容
	content := strings.Join(item.Columns, " ")
	return style.Render(content)
}

// GetID 获取资源列表项ID
func (item *ResourceListItem) GetID() string {
	return item.ID
}

// NavigationListItem 导航列表项
type NavigationListItem struct {
	ID       string
	Key      string
	Name     string
	Icon     string
	Selected bool
}

// Render 渲染导航列表项
func (item *NavigationListItem) Render(selected bool, width int) string {
	var style lipgloss.Style
	if selected {
		style = styles.SidebarSelectedStyle.Width(width - 4)
	} else {
		style = styles.SidebarItemStyle.Width(width - 4)
	}

	var content string
	if selected {
		content = fmt.Sprintf("▶ %s %s", item.Icon, item.Name)
	} else {
		content = fmt.Sprintf("  %s %s", item.Icon, item.Name)
	}

	return style.Render(content)
}

// GetID 获取导航列表项ID
func (item *NavigationListItem) GetID() string {
	return item.ID
}
