package components

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/tui/styles"
)

// Panel 通用面板组件
type Panel struct {
	title   string
	content string
	width   int
	height  int
	style   lipgloss.Style
}

// NewPanel 创建新的面板
func NewPanel(title string) *Panel {
	return &Panel{
		title: title,
		style: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(styles.Border).
			Background(styles.Surface),
	}
}

// SetContent 设置面板内容
func (p *Panel) SetContent(content string) *Panel {
	p.content = content
	return p
}

// SetSize 设置面板大小
func (p *Panel) SetSize(width, height int) *Panel {
	p.width = width
	p.height = height
	return p
}

// SetStyle 设置面板样式
func (p *Panel) SetStyle(style lipgloss.Style) *Panel {
	p.style = style
	return p
}

// Render 渲染面板
func (p *Panel) Render() string {
	// 计算内容区域大小
	contentWidth := p.width - 2  // 减去边框
	contentHeight := p.height - 2 // 减去边框和标题

	if contentWidth < 0 {
		contentWidth = 0
	}
	if contentHeight < 0 {
		contentHeight = 0
	}

	// 创建标题样式
	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(styles.Text).
		Background(styles.Surface).
		Padding(0, 1)

	// 创建内容样式
	contentStyle := lipgloss.NewStyle().
		Width(contentWidth).
		Height(contentHeight).
		Padding(1, 1).
		Background(styles.Background)

	// 如果有标题，添加标题
	var renderedContent string
	if p.title != "" {
		title := titleStyle.Render(p.title)
		content := contentStyle.Render(p.content)
		renderedContent = lipgloss.JoinVertical(lipgloss.Left, title, content)
	} else {
		renderedContent = contentStyle.Render(p.content)
	}

	// 应用面板样式
	return p.style.
		Width(p.width).
		Height(p.height).
		Render(renderedContent)
}

// HeaderPanel 创建头部面板
func HeaderPanel(title, subtitle string, width int) *Panel {
	content := title
	if subtitle != "" {
		// 计算填充空间
		totalLen := len(title) + len(subtitle)
		padding := ""
		if width > totalLen+4 { // +4 for padding
			padding = lipgloss.NewStyle().
				Width(width - totalLen - 4).
				Render("")
		}
		content = title + padding + subtitle
	}

	return NewPanel("").
		SetContent(content).
		SetSize(width, 3).
		SetStyle(lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder(), false, false, true, false).
			BorderForeground(styles.Border).
			Background(styles.Surface).
			Foreground(styles.Text).
			Bold(true).
			Padding(0, 1))
}

// FooterPanel 创建底部面板
func FooterPanel(content string, width int) *Panel {
	return NewPanel("").
		SetContent(content).
		SetSize(width, 3).
		SetStyle(lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder(), true, false, false, false).
			BorderForeground(styles.Border).
			Background(styles.Surface).
			Foreground(styles.TextMuted).
			Padding(0, 1))
}

// SidebarPanel 创建侧边栏面板
func SidebarPanel(content string, width, height int) *Panel {
	return NewPanel("").
		SetContent(content).
		SetSize(width, height).
		SetStyle(lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder(), false, true, false, false).
			BorderForeground(styles.Border).
			Background(styles.Surface).
			Padding(1, 1))
}

// ContentPanel 创建内容面板
func ContentPanel(title, content string, width, height int) *Panel {
	panel := NewPanel(title).
		SetContent(content).
		SetSize(width, height)

	if title != "" {
		panel.SetStyle(lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(styles.Border).
			Background(styles.Background))
	} else {
		panel.SetStyle(lipgloss.NewStyle().
			Background(styles.Background).
			Padding(1, 1))
	}

	return panel
}
