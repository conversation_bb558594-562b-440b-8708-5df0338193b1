package components

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/tui/styles"
)

// StatusBar 状态栏组件
type StatusBar struct {
	leftItems  []StatusItem
	rightItems []StatusItem
	width      int
}

// StatusItem 状态栏项
type StatusItem struct {
	Key     string
	Label   string
	Value   string
	Style   lipgloss.Style
	IsKey   bool // 是否是快捷键
}

// NewStatusBar 创建新的状态栏
func NewStatusBar(width int) *StatusBar {
	return &StatusBar{
		leftItems:  make([]StatusItem, 0),
		rightItems: make([]StatusItem, 0),
		width:      width,
	}
}

// AddLeftItem 添加左侧项
func (sb *StatusBar) AddLeftItem(item StatusItem) *StatusBar {
	sb.leftItems = append(sb.leftItems, item)
	return sb
}

// AddRightItem 添加右侧项
func (sb *StatusBar) AddRightItem(item StatusItem) *StatusBar {
	sb.rightItems = append(sb.rightItems, item)
	return sb
}

// AddShortcut 添加快捷键
func (sb *StatusBar) AddShortcut(key, description string) *StatusBar {
	item := StatusItem{
		Key:   key,
		Label: description,
		Style: styles.FooterKeyStyle,
		IsKey: true,
	}
	sb.leftItems = append(sb.leftItems, item)
	return sb
}

// AddInfo 添加信息项
func (sb *StatusBar) AddInfo(label, value string) *StatusBar {
	item := StatusItem{
		Label: label,
		Value: value,
		Style: lipgloss.NewStyle().Foreground(styles.TextMuted),
		IsKey: false,
	}
	sb.rightItems = append(sb.rightItems, item)
	return sb
}

// SetWidth 设置宽度
func (sb *StatusBar) SetWidth(width int) *StatusBar {
	sb.width = width
	return sb
}

// Render 渲染状态栏
func (sb *StatusBar) Render() string {
	// 渲染左侧项
	var leftParts []string
	for _, item := range sb.leftItems {
		if item.IsKey {
			keyStyle := styles.FooterKeyStyle
			part := fmt.Sprintf("%s: %s", keyStyle.Render(item.Key), item.Label)
			leftParts = append(leftParts, part)
		} else {
			if item.Value != "" {
				part := fmt.Sprintf("%s: %s", item.Label, item.Value)
				leftParts = append(leftParts, item.Style.Render(part))
			} else {
				leftParts = append(leftParts, item.Style.Render(item.Label))
			}
		}
	}

	// 渲染右侧项
	var rightParts []string
	for _, item := range sb.rightItems {
		if item.Value != "" {
			part := fmt.Sprintf("%s: %s", item.Label, item.Value)
			rightParts = append(rightParts, item.Style.Render(part))
		} else {
			rightParts = append(rightParts, item.Style.Render(item.Label))
		}
	}

	// 组合左右两侧
	leftContent := strings.Join(leftParts, "  ")
	rightContent := strings.Join(rightParts, " | ")

	// 计算填充
	contentLen := lipgloss.Width(leftContent) + lipgloss.Width(rightContent)
	padding := ""
	if sb.width > contentLen+4 { // +4 for padding
		padding = strings.Repeat(" ", sb.width-contentLen-4)
	}

	content := leftContent + padding + rightContent

	// 应用状态栏样式
	return styles.FooterStyle.
		Width(sb.width).
		Render(content)
}

// DefaultShortcuts 创建默认快捷键状态栏
func DefaultShortcuts(width int) *StatusBar {
	return NewStatusBar(width).
		AddShortcut("q", "Quit").
		AddShortcut("r", "Refresh").
		AddShortcut("↑/↓", "Navigate")
}

// ResourceStatusBar 创建资源状态栏
func ResourceStatusBar(width int, resourceCount int, lastUpdate string) *StatusBar {
	sb := NewStatusBar(width).
		AddShortcut("q", "Quit").
		AddShortcut("r", "Refresh").
		AddShortcut("1-5", "Switch")

	if resourceCount >= 0 {
		countStyle := lipgloss.NewStyle().Foreground(styles.Success)
		sb.AddRightItem(StatusItem{
			Label: "Resources",
			Value: countStyle.Render(fmt.Sprintf("%d", resourceCount)),
			Style: lipgloss.NewStyle().Foreground(styles.TextMuted),
		})
	}

	if lastUpdate != "" {
		timeStyle := lipgloss.NewStyle().Foreground(styles.TextDim)
		sb.AddRightItem(StatusItem{
			Label: "Updated",
			Value: timeStyle.Render(lastUpdate),
			Style: lipgloss.NewStyle().Foreground(styles.TextMuted),
		})
	}

	return sb
}

// ClusterStatusBar 创建集群状态栏
func ClusterStatusBar(width int, clusterName, namespace string) *StatusBar {
	sb := NewStatusBar(width)

	if clusterName != "" {
		sb.AddRightItem(StatusItem{
			Label: "Cluster",
			Value: clusterName,
			Style: lipgloss.NewStyle().Foreground(styles.Info),
		})
	}

	if namespace != "" {
		sb.AddRightItem(StatusItem{
			Label: "Namespace",
			Value: namespace,
			Style: lipgloss.NewStyle().Foreground(styles.Primary),
		})
	}

	return sb
}

// CombinedStatusBar 创建组合状态栏
func CombinedStatusBar(width int, resourceCount int, lastUpdate, clusterName, namespace string) *StatusBar {
	sb := NewStatusBar(width).
		AddShortcut("q", "Quit").
		AddShortcut("r", "Refresh").
		AddShortcut("1-5", "Switch")

	// 右侧信息
	var rightItems []string

	if resourceCount >= 0 {
		countStyle := lipgloss.NewStyle().Foreground(styles.Success)
		rightItems = append(rightItems, fmt.Sprintf("Resources: %s", countStyle.Render(fmt.Sprintf("%d", resourceCount))))
	}

	if lastUpdate != "" {
		timeStyle := lipgloss.NewStyle().Foreground(styles.TextDim)
		rightItems = append(rightItems, fmt.Sprintf("Updated: %s", timeStyle.Render(lastUpdate)))
	}

	if len(rightItems) > 0 {
		sb.AddRightItem(StatusItem{
			Label: strings.Join(rightItems, " | "),
			Style: lipgloss.NewStyle().Foreground(styles.TextMuted),
		})
	}

	return sb
}
