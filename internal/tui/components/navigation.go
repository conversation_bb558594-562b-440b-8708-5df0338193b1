package components

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/models"
)

// NavigationItem 导航项
type NavigationItem struct {
	Key          string
	Name         string
	Icon         string
	ResourceType models.ResourceType
}

// Navigation 导航组件
type Navigation struct {
	items    []NavigationItem
	selected int
	width    int
	height   int
}

// NewNavigation 创建新的导航
func NewNavigation(width, height int) *Navigation {
	return &Navigation{
		items:    getDefaultNavigationItems(),
		selected: 0,
		width:    width,
		height:   height,
	}
}

// getDefaultNavigationItems 获取默认导航项
func getDefaultNavigationItems() []NavigationItem {
	return []NavigationItem{
		{
			Key:          "1",
			Name:         "Pods",
			Icon:         "🟢",
			ResourceType: models.ResourceTypePod,
		},
		{
			Key:          "2",
			Name:         "Services",
			Icon:         "🔵",
			ResourceType: models.ResourceTypeService,
		},
		{
			Key:          "3",
			Name:         "Deployments",
			Icon:         "🚀",
			ResourceType: models.ResourceTypeDeployment,
		},
		{
			Key:          "4",
			Name:         "Nodes",
			Icon:         "🖥️",
			ResourceType: models.ResourceTypeNode,
		},
		{
			Key:          "5",
			Name:         "Namespaces",
			Icon:         "📁",
			ResourceType: models.ResourceTypeNamespace,
		},
	}
}

// SetSelected 设置选中项
func (n *Navigation) SetSelected(resourceType models.ResourceType) *Navigation {
	for i, item := range n.items {
		if item.ResourceType == resourceType {
			n.selected = i
			break
		}
	}
	return n
}

// GetSelected 获取选中的资源类型
func (n *Navigation) GetSelected() models.ResourceType {
	if n.selected >= 0 && n.selected < len(n.items) {
		return n.items[n.selected].ResourceType
	}
	return models.ResourceTypePod
}

// GetSelectedItem 获取选中的导航项
func (n *Navigation) GetSelectedItem() NavigationItem {
	if n.selected >= 0 && n.selected < len(n.items) {
		return n.items[n.selected]
	}
	return n.items[0]
}

// SelectByKey 根据按键选择
func (n *Navigation) SelectByKey(key string) bool {
	for i, item := range n.items {
		if item.Key == key {
			n.selected = i
			return true
		}
	}
	return false
}

// MoveUp 向上移动
func (n *Navigation) MoveUp() {
	if n.selected > 0 {
		n.selected--
	}
}

// MoveDown 向下移动
func (n *Navigation) MoveDown() {
	if n.selected < len(n.items)-1 {
		n.selected++
	}
}

// Render renders navigation with "Workloads" title and dropdown arrow
func (n *Navigation) Render() string {
	// Create workloads header with dropdown arrow
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#cdd6f4")). // Text color
		Padding(0, 1)

	workloadsHeader := headerStyle.Render("▼ Workloads")

	// Convert to list items
	var listItems []ListItem
	for i, item := range n.items {
		listItem := &NavigationListItem{
			ID:       item.Key,
			Key:      item.Key,
			Name:     item.Name,
			Icon:     item.Icon,
			Selected: i == n.selected,
		}
		listItems = append(listItems, listItem)
	}

	// Create list without header (we'll add our own)
	list := NewList().
		SetItems(listItems).
		SetSelected(n.selected).
		SetSize(n.width, n.height-2) // -2 for header space

	listContent := list.Render()

	// Combine header and list
	content := lipgloss.JoinVertical(lipgloss.Left, workloadsHeader, listContent)

	return content
}

// ResourceNavigation 创建资源导航
func ResourceNavigation(currentResourceType models.ResourceType, width, height int) *Navigation {
	nav := NewNavigation(width, height)
	nav.SetSelected(currentResourceType)
	return nav
}

// GetNavigationItemByResourceType 根据资源类型获取导航项
func GetNavigationItemByResourceType(resourceType models.ResourceType) NavigationItem {
	items := getDefaultNavigationItems()
	for _, item := range items {
		if item.ResourceType == resourceType {
			return item
		}
	}
	return items[0] // 默认返回第一个
}

// GetResourceTypeByKey 根据按键获取资源类型
func GetResourceTypeByKey(key string) (models.ResourceType, bool) {
	items := getDefaultNavigationItems()
	for _, item := range items {
		if item.Key == key {
			return item.ResourceType, true
		}
	}
	return models.ResourceTypePod, false
}
