package components

import (
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/models"
)

// NavigationItem 导航项
type NavigationItem struct {
	Key          string
	Name         string
	Icon         string
	ResourceType models.ResourceType
}

// Navigation 导航组件
type Navigation struct {
	items    []NavigationItem
	selected int
	width    int
	height   int
}

// NewNavigation 创建新的导航
func NewNavigation(width, height int) *Navigation {
	return &Navigation{
		items:    getDefaultNavigationItems(),
		selected: 0,
		width:    width,
		height:   height,
	}
}

// getDefaultNavigationItems 获取默认导航项
func getDefaultNavigationItems() []NavigationItem {
	return []NavigationItem{
		{
			Key:          "1",
			Name:         "Pods",
			Icon:         "🟢",
			ResourceType: models.ResourceTypePod,
		},
		{
			Key:          "2",
			Name:         "Services",
			Icon:         "🔵",
			ResourceType: models.ResourceTypeService,
		},
		{
			Key:          "3",
			Name:         "Deployments",
			Icon:         "🚀",
			ResourceType: models.ResourceTypeDeployment,
		},
		{
			Key:          "4",
			Name:         "Nodes",
			Icon:         "🖥️",
			ResourceType: models.ResourceTypeNode,
		},
		{
			Key:          "5",
			Name:         "Namespaces",
			Icon:         "📁",
			ResourceType: models.ResourceTypeNamespace,
		},
	}
}

// SetSelected 设置选中项
func (n *Navigation) SetSelected(resourceType models.ResourceType) *Navigation {
	for i, item := range n.items {
		if item.ResourceType == resourceType {
			n.selected = i
			break
		}
	}
	return n
}

// GetSelected 获取选中的资源类型
func (n *Navigation) GetSelected() models.ResourceType {
	if n.selected >= 0 && n.selected < len(n.items) {
		return n.items[n.selected].ResourceType
	}
	return models.ResourceTypePod
}

// GetSelectedItem 获取选中的导航项
func (n *Navigation) GetSelectedItem() NavigationItem {
	if n.selected >= 0 && n.selected < len(n.items) {
		return n.items[n.selected]
	}
	return n.items[0]
}

// SelectByKey 根据按键选择
func (n *Navigation) SelectByKey(key string) bool {
	for i, item := range n.items {
		if item.Key == key {
			n.selected = i
			return true
		}
	}
	return false
}

// MoveUp 向上移动
func (n *Navigation) MoveUp() {
	if n.selected > 0 {
		n.selected--
	}
}

// MoveDown 向下移动
func (n *Navigation) MoveDown() {
	if n.selected < len(n.items)-1 {
		n.selected++
	}
}

// Render renders navigation matching the UI design
func (n *Navigation) Render() string {
	// Create workloads header
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#4c4f69")). // Latte Text
		Padding(0, 1).
		Width(n.width - 4) // -4 for border

	workloadsHeader := headerStyle.Render("Workloads")

	// Create navigation items
	var lines []string
	lines = append(lines, workloadsHeader)

	for i, item := range n.items {
		var style lipgloss.Style
		var content string

		if i == n.selected {
			// Selected item style - Latte colors
			style = lipgloss.NewStyle().
				Bold(true).
				Foreground(lipgloss.Color("#eff1f5")). // Latte Base (white text)
				Background(lipgloss.Color("#8839ef")). // Latte Mauve
				Padding(0, 1).
				Width(n.width - 4) // -4 for border
			content = item.Name // Remove icon
		} else {
			// Normal item style - Latte colors
			style = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#6c6f85")). // Latte Subtext0
				Padding(0, 1).
				Width(n.width - 4) // -4 for border
			content = item.Name // Remove icon
		}

		lines = append(lines, style.Render(content))
	}

	// Fill remaining space
	for len(lines) < n.height-2 { // -2 for border
		lines = append(lines, "")
	}

	content := strings.Join(lines, "\n")

	// Wrap with rounded border - Latte colors
	borderStyle := lipgloss.NewStyle().
		Border(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("#acb0be")). // Latte Surface2
		Background(lipgloss.Color("#eff1f5")).       // Latte Base
		Width(n.width).
		Height(n.height)

	return borderStyle.Render(content)
}

// ResourceNavigation creates resource navigation
func ResourceNavigation(currentResourceType models.ResourceType, width, height int) *Navigation {
	nav := NewNavigation(width, height)
	nav.SetSelected(currentResourceType)
	return nav
}

// GetNavigationItemByResourceType gets navigation item by resource type
func GetNavigationItemByResourceType(resourceType models.ResourceType) NavigationItem {
	items := getDefaultNavigationItems()
	for _, item := range items {
		if item.ResourceType == resourceType {
			return item
		}
	}
	return items[0] // 默认返回第一个
}

// GetResourceTypeByKey gets resource type by key
func GetResourceTypeByKey(key string) (models.ResourceType, bool) {
	items := getDefaultNavigationItems()
	for _, item := range items {
		if item.Key == key {
			return item.ResourceType, true
		}
	}
	return models.ResourceTypePod, false
}
