package styles

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/config"
)

// ThemeManager 主题管理器
type ThemeManager struct {
	config *config.AppConfig
	colors *config.ColorsConfig
}

// NewThemeManager 创建新的主题管理器
func NewThemeManager(appConfig *config.AppConfig) *ThemeManager {
	return &ThemeManager{
		config: appConfig,
		colors: &appConfig.Theme.Colors,
	}
}

// ApplyTheme 应用主题到全局样式变量
func (tm *ThemeManager) ApplyTheme() {
	// 更新全局颜色变量
	Primary = lipgloss.Color(tm.colors.Primary)
	Secondary = lipgloss.Color(tm.colors.Secondary)
	Success = lipgloss.Color(tm.colors.Success)
	Warning = lipgloss.Color(tm.colors.Warning)
	Error = lipgloss.Color(tm.colors.Error)
	Info = lipgloss.Color(tm.colors.Info)

	Background = lipgloss.Color(tm.colors.Background)
	Surface = lipgloss.Color(tm.colors.Surface)
	Border = lipgloss.Color(tm.colors.Border)
	Text = lipgloss.Color(tm.colors.Text)
	TextMuted = lipgloss.Color(tm.colors.TextMuted)
	TextDim = lipgloss.Color(tm.colors.TextDim)

	StatusRunning = lipgloss.Color(tm.colors.StatusRunning)
	StatusPending = lipgloss.Color(tm.colors.StatusPending)
	StatusFailed = lipgloss.Color(tm.colors.StatusFailed)
	StatusTerminating = lipgloss.Color(tm.colors.StatusTerminating)
	StatusUnknown = lipgloss.Color(tm.colors.StatusUnknown)

	Selected = lipgloss.Color(tm.colors.Selected)
	Highlighted = lipgloss.Color(tm.colors.Highlighted)

	// 重新创建样式
	tm.updateStyles()
}

// updateStyles 更新所有样式
func (tm *ThemeManager) updateStyles() {
	// Base styles
	BaseStyle = lipgloss.NewStyle().
		Foreground(Text).
		Background(Background)

	// Header styles
	HeaderStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Surface).
		Padding(0, 1).
		Border(lipgloss.RoundedBorder(), false, false, true, false).
		BorderForeground(Border)

	// Sidebar styles
	SidebarStyle = lipgloss.NewStyle().
		Background(Surface).
		Border(lipgloss.RoundedBorder(), false, true, false, false).
		BorderForeground(Border).
		Padding(1, 1)

	SidebarItemStyle = lipgloss.NewStyle().
		Foreground(TextMuted).
		Padding(0, 1)

	SidebarSelectedStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Selected).
		Padding(0, 1)

	// Content styles
	ContentStyle = lipgloss.NewStyle().
		Background(Background).
		Padding(1, 1)

	ContentHeaderStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Surface).
		Padding(0, 1)

	ContentRowStyle = lipgloss.NewStyle().
		Foreground(Text).
		Padding(0, 1)

	ContentSelectedRowStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Selected).
		Padding(0, 1)

	// Footer styles
	FooterStyle = lipgloss.NewStyle().
		Foreground(TextMuted).
		Background(Surface).
		Border(lipgloss.RoundedBorder(), true, false, false, false).
		BorderForeground(Border).
		Padding(0, 1)

	FooterKeyStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Primary)

	// Status styles
	StatusStyle = lipgloss.NewStyle().
		Bold(true)

	// Empty state styles
	EmptyStyle = lipgloss.NewStyle().
		Foreground(TextMuted).
		Italic(true).
		Padding(2, 0)

	// Error styles
	ErrorStyle = lipgloss.NewStyle().
		Foreground(Error).
		Bold(true)

	// Loading styles
	LoadingStyle = lipgloss.NewStyle().
		Foreground(Info).
		Italic(true)
}

// GetDefaultColors returns default colors (Catppuccin Mocha)
func GetDefaultColors() config.ColorsConfig {
	return getCatppuccinMochaColors()
}

// getCatppuccinMochaColors returns Catppuccin Mocha theme colors
func getCatppuccinMochaColors() config.ColorsConfig {
	return config.ColorsConfig{
		Primary:   "#cba6f7", // Mauve
		Secondary: "#89b4fa", // Blue
		Success:   "#a6e3a1", // Green
		Warning:   "#f9e2af", // Yellow
		Error:     "#f38ba8", // Red
		Info:      "#89dceb", // Sky

		Background: "#1e1e2e", // Base
		Surface:    "#313244", // Surface0
		Border:     "#585b70", // Surface2
		Text:       "#cdd6f4", // Text
		TextMuted:  "#a6adc8", // Subtext0
		TextDim:    "#9399b2", // Overlay2

		StatusRunning:     "#a6e3a1", // Green
		StatusPending:     "#f9e2af", // Yellow
		StatusFailed:      "#f38ba8", // Red
		StatusTerminating: "#fab387", // Peach
		StatusUnknown:     "#a6adc8", // Subtext0

		Selected:    "#45475a", // Surface1
		Highlighted: "#585b70", // Surface2
	}
}
