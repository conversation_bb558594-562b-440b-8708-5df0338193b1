package styles

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/config"
)

// ThemeManager 主题管理器
type ThemeManager struct {
	config *config.AppConfig
	colors *config.ColorsConfig
}

// NewThemeManager 创建新的主题管理器
func NewThemeManager(appConfig *config.AppConfig) *ThemeManager {
	return &ThemeManager{
		config: appConfig,
		colors: &appConfig.Theme.Colors,
	}
}

// ApplyTheme 应用主题到全局样式变量
func (tm *ThemeManager) ApplyTheme() {
	// 更新全局颜色变量
	Primary = lipgloss.Color(tm.colors.Primary)
	Secondary = lipgloss.Color(tm.colors.Secondary)
	Success = lipgloss.Color(tm.colors.Success)
	Warning = lipgloss.Color(tm.colors.Warning)
	Error = lipgloss.Color(tm.colors.Error)
	Info = lipgloss.Color(tm.colors.Info)

	Background = lipgloss.Color(tm.colors.Background)
	Surface = lipgloss.Color(tm.colors.Surface)
	Border = lipgloss.Color(tm.colors.Border)
	Text = lipgloss.Color(tm.colors.Text)
	TextMuted = lipgloss.Color(tm.colors.TextMuted)
	TextDim = lipgloss.Color(tm.colors.TextDim)

	StatusRunning = lipgloss.Color(tm.colors.StatusRunning)
	StatusPending = lipgloss.Color(tm.colors.StatusPending)
	StatusFailed = lipgloss.Color(tm.colors.StatusFailed)
	StatusTerminating = lipgloss.Color(tm.colors.StatusTerminating)
	StatusUnknown = lipgloss.Color(tm.colors.StatusUnknown)

	Selected = lipgloss.Color(tm.colors.Selected)
	Highlighted = lipgloss.Color(tm.colors.Highlighted)

	// 重新创建样式
	tm.updateStyles()
}

// updateStyles 更新所有样式
func (tm *ThemeManager) updateStyles() {
	// Base styles
	BaseStyle = lipgloss.NewStyle().
		Foreground(Text).
		Background(Background)

	// Header styles
	HeaderStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Surface).
		Padding(0, 1).
		Border(lipgloss.RoundedBorder(), false, false, true, false).
		BorderForeground(Border)

	// Sidebar styles
	SidebarStyle = lipgloss.NewStyle().
		Background(Surface).
		Border(lipgloss.RoundedBorder(), false, true, false, false).
		BorderForeground(Border).
		Padding(1, 1)

	SidebarItemStyle = lipgloss.NewStyle().
		Foreground(TextMuted).
		Padding(0, 1)

	SidebarSelectedStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Selected).
		Padding(0, 1)

	// Content styles
	ContentStyle = lipgloss.NewStyle().
		Background(Background).
		Padding(1, 1)

	ContentHeaderStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Surface).
		Padding(0, 1)

	ContentRowStyle = lipgloss.NewStyle().
		Foreground(Text).
		Padding(0, 1)

	ContentSelectedRowStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Text).
		Background(Selected).
		Padding(0, 1)

	// Footer styles
	FooterStyle = lipgloss.NewStyle().
		Foreground(TextMuted).
		Background(Surface).
		Border(lipgloss.RoundedBorder(), true, false, false, false).
		BorderForeground(Border).
		Padding(0, 1)

	FooterKeyStyle = lipgloss.NewStyle().
		Bold(true).
		Foreground(Primary)

	// Status styles
	StatusStyle = lipgloss.NewStyle().
		Bold(true)

	// Empty state styles
	EmptyStyle = lipgloss.NewStyle().
		Foreground(TextMuted).
		Italic(true).
		Padding(2, 0)

	// Error styles
	ErrorStyle = lipgloss.NewStyle().
		Foreground(Error).
		Bold(true)

	// Loading styles
	LoadingStyle = lipgloss.NewStyle().
		Foreground(Info).
		Italic(true)
}

// GetCatppuccinVariantColors 根据变体获取Catppuccin颜色配置
func GetCatppuccinVariantColors(variant string) config.ColorsConfig {
	switch variant {
	case "latte":
		return getCatppuccinLatteColors()
	case "frappe":
		return getCatppuccinFrappeColors()
	case "macchiato":
		return getCatppuccinMacchiatoColors()
	case "mocha":
		fallthrough
	default:
		return getCatppuccinMochaColors()
	}
}

// getCatppuccinLatteColors 返回Catppuccin Latte主题颜色
func getCatppuccinLatteColors() config.ColorsConfig {
	return config.ColorsConfig{
		Primary:   "#8839ef", // Mauve
		Secondary: "#1e66f5", // Blue
		Success:   "#40a02b", // Green
		Warning:   "#df8e1d", // Yellow
		Error:     "#d20f39", // Red
		Info:      "#04a5e5", // Sky

		Background: "#eff1f5", // Base
		Surface:    "#e6e9ef", // Surface0
		Border:     "#acb0be", // Surface2
		Text:       "#4c4f69", // Text
		TextMuted:  "#6c6f85", // Subtext0
		TextDim:    "#7c7f93", // Overlay2

		StatusRunning:     "#40a02b", // Green
		StatusPending:     "#df8e1d", // Yellow
		StatusFailed:      "#d20f39", // Red
		StatusTerminating: "#fe640b", // Peach
		StatusUnknown:     "#6c6f85", // Subtext0

		Selected:    "#dce0e8", // Surface1
		Highlighted: "#acb0be", // Surface2
	}
}

// getCatppuccinFrappeColors 返回Catppuccin Frappe主题颜色
func getCatppuccinFrappeColors() config.ColorsConfig {
	return config.ColorsConfig{
		Primary:   "#ca9ee6", // Mauve
		Secondary: "#8caaee", // Blue
		Success:   "#a6d189", // Green
		Warning:   "#e5c890", // Yellow
		Error:     "#e78284", // Red
		Info:      "#99d1db", // Sky

		Background: "#303446", // Base
		Surface:    "#414559", // Surface0
		Border:     "#626880", // Surface2
		Text:       "#c6d0f5", // Text
		TextMuted:  "#a5adce", // Subtext0
		TextDim:    "#949cbb", // Overlay2

		StatusRunning:     "#a6d189", // Green
		StatusPending:     "#e5c890", // Yellow
		StatusFailed:      "#e78284", // Red
		StatusTerminating: "#ef9f76", // Peach
		StatusUnknown:     "#a5adce", // Subtext0

		Selected:    "#51576d", // Surface1
		Highlighted: "#626880", // Surface2
	}
}

// getCatppuccinMacchiatoColors 返回Catppuccin Macchiato主题颜色
func getCatppuccinMacchiatoColors() config.ColorsConfig {
	return config.ColorsConfig{
		Primary:   "#c6a0f6", // Mauve
		Secondary: "#8aadf4", // Blue
		Success:   "#a6da95", // Green
		Warning:   "#eed49f", // Yellow
		Error:     "#ed8796", // Red
		Info:      "#91d7e3", // Sky

		Background: "#24273a", // Base
		Surface:    "#363a4f", // Surface0
		Border:     "#5b6078", // Surface2
		Text:       "#cad3f5", // Text
		TextMuted:  "#a5adcb", // Subtext0
		TextDim:    "#939ab7", // Overlay2

		StatusRunning:     "#a6da95", // Green
		StatusPending:     "#eed49f", // Yellow
		StatusFailed:      "#ed8796", // Red
		StatusTerminating: "#f5a97f", // Peach
		StatusUnknown:     "#a5adcb", // Subtext0

		Selected:    "#494d64", // Surface1
		Highlighted: "#5b6078", // Surface2
	}
}

// getCatppuccinMochaColors 返回Catppuccin Mocha主题颜色
func getCatppuccinMochaColors() config.ColorsConfig {
	return config.ColorsConfig{
		Primary:   "#cba6f7", // Mauve
		Secondary: "#89b4fa", // Blue
		Success:   "#a6e3a1", // Green
		Warning:   "#f9e2af", // Yellow
		Error:     "#f38ba8", // Red
		Info:      "#89dceb", // Sky

		Background: "#1e1e2e", // Base
		Surface:    "#313244", // Surface0
		Border:     "#585b70", // Surface2
		Text:       "#cdd6f4", // Text
		TextMuted:  "#a6adc8", // Subtext0
		TextDim:    "#9399b2", // Overlay2

		StatusRunning:     "#a6e3a1", // Green
		StatusPending:     "#f9e2af", // Yellow
		StatusFailed:      "#f38ba8", // Red
		StatusTerminating: "#fab387", // Peach
		StatusUnknown:     "#a6adc8", // Subtext0

		Selected:    "#45475a", // Surface1
		Highlighted: "#585b70", // Surface2
	}
}
