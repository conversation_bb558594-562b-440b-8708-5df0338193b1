package k8s

import (
	"context"
	"fmt"

	"github.com/liyujun-dev/kui/internal/config"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

// Client Kubernetes 客户端封装
type Client struct {
	clientset *kubernetes.Clientset
	config    *rest.Config
	namespace string
}

// NewClient 创建新的 Kubernetes 客户端
func NewClient() (*Client, error) {
	restConfig, err := config.LoadKubeConfig()
	if err != nil {
		return nil, fmt.Errorf("加载 kubeconfig 失败: %w", err)
	}

	clientset, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("创建 Kubernetes 客户端失败: %w", err)
	}

	// 获取当前命名空间，如果没有则使用default
	_, namespace, err := config.GetClusterInfo()
	if err != nil {
		namespace = "default" // 如果获取失败，使用默认命名空间
	}

	return &Client{
		clientset: clientset,
		config:    restConfig,
		namespace: namespace,
	}, nil
}

// GetClientset 返回 Kubernetes clientset
func (c *Client) GetClientset() *kubernetes.Clientset {
	return c.clientset
}

// GetConfig 返回 REST 配置
func (c *Client) GetConfig() *rest.Config {
	return c.config
}

// SetNamespace 设置当前命名空间
func (c *Client) SetNamespace(namespace string) {
	c.namespace = namespace
}

// GetNamespace 获取当前命名空间
func (c *Client) GetNamespace() string {
	return c.namespace
}

// TestConnection 测试连接
func (c *Client) TestConnection(ctx context.Context) error {
	_, err := c.clientset.Discovery().ServerVersion()
	if err != nil {
		return fmt.Errorf("连接 Kubernetes 集群失败: %w", err)
	}
	return nil
}

// GetServerVersion 获取服务器版本
func (c *Client) GetServerVersion(ctx context.Context) (string, error) {
	version, err := c.clientset.Discovery().ServerVersion()
	if err != nil {
		return "", fmt.Errorf("获取服务器版本失败: %w", err)
	}
	return version.String(), nil
}
