package config

import (
	"os"
	"path/filepath"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

// Config Kubernetes连接配置（仅用于K8s客户端，不存储在配置文件中）
type Config struct {
	// 这个结构体现在只用于运行时K8s连接，不再存储应用配置
	// 所有应用配置都移到了AppConfig中
}

// NewKubeConfig 创建新的Kubernetes配置（运行时使用）
func NewKubeConfig() *Config {
	return &Config{}
}

// getDefaultKubeConfigPath 获取默认的 kubeconfig 路径
func getDefaultKubeConfigPath() string {
	if kubeconfig := os.Getenv("KUBECONFIG"); kubeconfig != "" {
		return kubeconfig
	}

	if home := homedir.HomeDir(); home != "" {
		return filepath.Join(home, ".kube", "config")
	}

	return ""
}

// LoadKubeConfig 加载 Kubernetes 配置（使用默认kubeconfig路径）
func LoadKubeConfig() (*rest.Config, error) {
	// 如果在集群内运行，使用集群内配置
	if config, err := rest.InClusterConfig(); err == nil {
		return config, nil
	}

	// 使用默认的 kubeconfig 文件路径
	kubeconfigPath := getDefaultKubeConfigPath()
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfigPath)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// GetCurrentContext 获取当前上下文
func GetCurrentContext() (string, error) {
	kubeconfigPath := getDefaultKubeConfigPath()
	if kubeconfigPath == "" {
		return "", nil
	}

	config, err := clientcmd.LoadFromFile(kubeconfigPath)
	if err != nil {
		return "", err
	}

	return config.CurrentContext, nil
}

// ListContexts 列出所有可用的上下文
func ListContexts() ([]string, error) {
	kubeconfigPath := getDefaultKubeConfigPath()
	if kubeconfigPath == "" {
		return nil, nil
	}

	config, err := clientcmd.LoadFromFile(kubeconfigPath)
	if err != nil {
		return nil, err
	}

	var contexts []string
	for name := range config.Contexts {
		contexts = append(contexts, name)
	}

	return contexts, nil
}

// GetClusterInfo 获取集群信息
func GetClusterInfo() (string, string, error) {
	kubeconfigPath := getDefaultKubeConfigPath()
	if kubeconfigPath == "" {
		return "", "", nil
	}

	config, err := clientcmd.LoadFromFile(kubeconfigPath)
	if err != nil {
		return "", "", err
	}

	currentContext := config.CurrentContext
	if currentContext == "" {
		return "", "", nil
	}

	context, exists := config.Contexts[currentContext]
	if !exists {
		return currentContext, "", nil
	}

	clusterName := context.Cluster
	namespace := context.Namespace
	if namespace == "" {
		namespace = "default"
	}

	return clusterName, namespace, nil
}
