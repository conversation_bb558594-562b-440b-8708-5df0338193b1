package config

import (
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

// AppConfig application configuration structure
type AppConfig struct {
	Theme       ThemeConfig       `yaml:"theme"`
	Keybindings KeybindingsConfig `yaml:"keybindings"`
	Layout      LayoutConfig      `yaml:"layout"`
	General     GeneralConfig     `yaml:"general"`
}

// ThemeConfig theme configuration (simplified - removed ColorScheme and Variant)
type ThemeConfig struct {
	Colors ColorsConfig `yaml:"colors"` // color configuration
}

// ColorsConfig color configuration
type ColorsConfig struct {
	// Primary colors
	Primary   string `yaml:"primary"`   // primary color
	Secondary string `yaml:"secondary"` // secondary color
	Success   string `yaml:"success"`   // success status color
	Warning   string `yaml:"warning"`   // warning status color
	Error     string `yaml:"error"`     // error status color
	Info      string `yaml:"info"`      // info status color

	// Neutral colors
	Background string `yaml:"background"` // background color
	Surface    string `yaml:"surface"`    // surface color
	Border     string `yaml:"border"`     // border color
	Text       string `yaml:"text"`       // text color
	TextMuted  string `yaml:"text_muted"` // muted text color
	TextDim    string `yaml:"text_dim"`   // dim text color

	// Status colors
	StatusRunning     string `yaml:"status_running"`     // running status color
	StatusPending     string `yaml:"status_pending"`     // pending status color
	StatusFailed      string `yaml:"status_failed"`      // failed status color
	StatusTerminating string `yaml:"status_terminating"` // terminating status color
	StatusUnknown     string `yaml:"status_unknown"`     // unknown status color

	// Selection colors
	Selected    string `yaml:"selected"`    // selected color
	Highlighted string `yaml:"highlighted"` // highlighted color
}

// KeybindingsConfig keybindings configuration
type KeybindingsConfig struct {
	Quit         []string `yaml:"quit"`
	Refresh      []string `yaml:"refresh"`
	Navigate     []string `yaml:"navigate"`
	SelectPod    string   `yaml:"select_pod"`
	SelectSvc    string   `yaml:"select_service"`
	SelectDeploy string   `yaml:"select_deployment"`
	SelectNode   string   `yaml:"select_node"`
	SelectNS     string   `yaml:"select_namespace"`
}

// LayoutConfig layout configuration
type LayoutConfig struct {
	SidebarWidth    int  `yaml:"sidebar_width"`
	ShowIcons       bool `yaml:"show_icons"`
	ShowStatusIcons bool `yaml:"show_status_icons"`
	CompactMode     bool `yaml:"compact_mode"`
}

// GeneralConfig general configuration
type GeneralConfig struct {
	RefreshInterval int    `yaml:"refresh_interval"` // seconds
	Language        string `yaml:"language"`         // en, zh
	LogLevel        string `yaml:"log_level"`        // debug, info, warn, error
}

// DefaultAppConfig returns default application configuration
func DefaultAppConfig() *AppConfig {
	return &AppConfig{
		Theme: ThemeConfig{
			Colors: getDefaultColors(),
		},
		Keybindings: KeybindingsConfig{
			Quit:         []string{"q", "ctrl+c"},
			Refresh:      []string{"r", "f5"},
			Navigate:     []string{"j", "k", "up", "down"},
			SelectPod:    "1",
			SelectSvc:    "2",
			SelectDeploy: "3",
			SelectNode:   "4",
			SelectNS:     "5",
		},
		Layout: LayoutConfig{
			SidebarWidth:    20,
			ShowIcons:       true,
			ShowStatusIcons: true,
			CompactMode:     false,
		},
		General: GeneralConfig{
			RefreshInterval: 5,
			Language:        "en",
			LogLevel:        "info",
		},
	}
}

// getDefaultColors returns default Catppuccin Mocha colors
func getDefaultColors() ColorsConfig {
	return ColorsConfig{
		// Primary colors - Catppuccin Mocha
		Primary:   "#cba6f7", // Mauve
		Secondary: "#89b4fa", // Blue
		Success:   "#a6e3a1", // Green
		Warning:   "#f9e2af", // Yellow
		Error:     "#f38ba8", // Red
		Info:      "#89dceb", // Sky

		// Neutral colors - Catppuccin Mocha
		Background: "#1e1e2e", // Base
		Surface:    "#313244", // Surface0
		Border:     "#585b70", // Surface2
		Text:       "#cdd6f4", // Text
		TextMuted:  "#a6adc8", // Subtext0
		TextDim:    "#9399b2", // Overlay2

		// Status colors
		StatusRunning:     "#a6e3a1", // Green
		StatusPending:     "#f9e2af", // Yellow
		StatusFailed:      "#f38ba8", // Red
		StatusTerminating: "#fab387", // Peach
		StatusUnknown:     "#a6adc8", // Subtext0

		// Selection colors
		Selected:    "#45475a", // Surface1
		Highlighted: "#585b70", // Surface2
	}
}

// getDefaultKubeConfigPath gets default kubeconfig path
func getDefaultKubeConfigPath() string {
	if kubeconfig := os.Getenv("KUBECONFIG"); kubeconfig != "" {
		return kubeconfig
	}

	if home := homedir.HomeDir(); home != "" {
		return filepath.Join(home, ".kube", "config")
	}

	return ""
}

// LoadKubeConfig 加载 Kubernetes 配置（使用默认kubeconfig路径）
func LoadKubeConfig() (*rest.Config, error) {
	// 如果在集群内运行，使用集群内配置
	if config, err := rest.InClusterConfig(); err == nil {
		return config, nil
	}

	// 使用默认的 kubeconfig 文件路径
	kubeconfigPath := getDefaultKubeConfigPath()
	config, err := clientcmd.BuildConfigFromFlags("", kubeconfigPath)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// GetCurrentContext 获取当前上下文
func GetCurrentContext() (string, error) {
	kubeconfigPath := getDefaultKubeConfigPath()
	if kubeconfigPath == "" {
		return "", nil
	}

	config, err := clientcmd.LoadFromFile(kubeconfigPath)
	if err != nil {
		return "", err
	}

	return config.CurrentContext, nil
}

// ListContexts 列出所有可用的上下文
func ListContexts() ([]string, error) {
	kubeconfigPath := getDefaultKubeConfigPath()
	if kubeconfigPath == "" {
		return nil, nil
	}

	config, err := clientcmd.LoadFromFile(kubeconfigPath)
	if err != nil {
		return nil, err
	}

	var contexts []string
	for name := range config.Contexts {
		contexts = append(contexts, name)
	}

	return contexts, nil
}

// GetClusterInfo 获取集群信息
func GetClusterInfo() (string, string, error) {
	kubeconfigPath := getDefaultKubeConfigPath()
	if kubeconfigPath == "" {
		return "", "", nil
	}

	config, err := clientcmd.LoadFromFile(kubeconfigPath)
	if err != nil {
		return "", "", err
	}

	currentContext := config.CurrentContext
	if currentContext == "" {
		return "", "", nil
	}

	context, exists := config.Contexts[currentContext]
	if !exists {
		return currentContext, "", nil
	}

	clusterName := context.Cluster
	namespace := context.Namespace
	if namespace == "" {
		namespace = "default"
	}

	return clusterName, namespace, nil
}

// LoadAppConfig loads application configuration
func LoadAppConfig(configPath string) (*AppConfig, error) {
	// Use default path if not specified
	if configPath == "" {
		configPath = getDefaultAppConfigPath()
	}

	// Create default config if file doesn't exist
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		defaultConfig := DefaultAppConfig()
		if err := SaveAppConfig(defaultConfig, configPath); err != nil {
			return nil, err
		}
		return defaultConfig, nil
	}

	// Read config file
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	// Parse YAML
	var config AppConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	// Validate and fill defaults
	validateAndFillDefaults(&config)

	return &config, nil
}

// SaveAppConfig saves application configuration
func SaveAppConfig(config *AppConfig, configPath string) error {
	// Ensure config directory exists
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return err
	}

	// Serialize to YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return err
	}

	// Write to file
	return os.WriteFile(configPath, data, 0644)
}

// getDefaultAppConfigPath gets default application config file path
func getDefaultAppConfigPath() string {
	if home := homedir.HomeDir(); home != "" {
		return filepath.Join(home, ".kui.yaml")
	}
	return ".kui.yaml"
}

// GetConfigPath gets config file path
func GetConfigPath(customPath string) string {
	if customPath != "" {
		return customPath
	}
	return getDefaultAppConfigPath()
}

// validateAndFillDefaults validates config and fills default values
func validateAndFillDefaults(config *AppConfig) {
	defaultConfig := DefaultAppConfig()

	// Validate colors config
	validateColorsConfig(&config.Theme.Colors, &defaultConfig.Theme.Colors)

	// Validate keybindings config
	if len(config.Keybindings.Quit) == 0 {
		config.Keybindings.Quit = defaultConfig.Keybindings.Quit
	}
	if len(config.Keybindings.Refresh) == 0 {
		config.Keybindings.Refresh = defaultConfig.Keybindings.Refresh
	}
	if len(config.Keybindings.Navigate) == 0 {
		config.Keybindings.Navigate = defaultConfig.Keybindings.Navigate
	}
	if config.Keybindings.SelectPod == "" {
		config.Keybindings.SelectPod = defaultConfig.Keybindings.SelectPod
	}
	if config.Keybindings.SelectSvc == "" {
		config.Keybindings.SelectSvc = defaultConfig.Keybindings.SelectSvc
	}
	if config.Keybindings.SelectDeploy == "" {
		config.Keybindings.SelectDeploy = defaultConfig.Keybindings.SelectDeploy
	}
	if config.Keybindings.SelectNode == "" {
		config.Keybindings.SelectNode = defaultConfig.Keybindings.SelectNode
	}
	if config.Keybindings.SelectNS == "" {
		config.Keybindings.SelectNS = defaultConfig.Keybindings.SelectNS
	}

	// Validate layout config
	if config.Layout.SidebarWidth <= 0 {
		config.Layout.SidebarWidth = defaultConfig.Layout.SidebarWidth
	}

	// Validate general config
	if config.General.RefreshInterval <= 0 {
		config.General.RefreshInterval = defaultConfig.General.RefreshInterval
	}
	if config.General.Language == "" {
		config.General.Language = defaultConfig.General.Language
	}
	if config.General.LogLevel == "" {
		config.General.LogLevel = defaultConfig.General.LogLevel
	}
}

// validateColorsConfig validates colors config and fills default values
func validateColorsConfig(config *ColorsConfig, defaultConfig *ColorsConfig) {
	if config.Primary == "" {
		config.Primary = defaultConfig.Primary
	}
	if config.Secondary == "" {
		config.Secondary = defaultConfig.Secondary
	}
	if config.Success == "" {
		config.Success = defaultConfig.Success
	}
	if config.Warning == "" {
		config.Warning = defaultConfig.Warning
	}
	if config.Error == "" {
		config.Error = defaultConfig.Error
	}
	if config.Info == "" {
		config.Info = defaultConfig.Info
	}
	if config.Background == "" {
		config.Background = defaultConfig.Background
	}
	if config.Surface == "" {
		config.Surface = defaultConfig.Surface
	}
	if config.Border == "" {
		config.Border = defaultConfig.Border
	}
	if config.Text == "" {
		config.Text = defaultConfig.Text
	}
	if config.TextMuted == "" {
		config.TextMuted = defaultConfig.TextMuted
	}
	if config.TextDim == "" {
		config.TextDim = defaultConfig.TextDim
	}
	if config.StatusRunning == "" {
		config.StatusRunning = defaultConfig.StatusRunning
	}
	if config.StatusPending == "" {
		config.StatusPending = defaultConfig.StatusPending
	}
	if config.StatusFailed == "" {
		config.StatusFailed = defaultConfig.StatusFailed
	}
	if config.StatusTerminating == "" {
		config.StatusTerminating = defaultConfig.StatusTerminating
	}
	if config.StatusUnknown == "" {
		config.StatusUnknown = defaultConfig.StatusUnknown
	}
	if config.Selected == "" {
		config.Selected = defaultConfig.Selected
	}
	if config.Highlighted == "" {
		config.Highlighted = defaultConfig.Highlighted
	}
}
