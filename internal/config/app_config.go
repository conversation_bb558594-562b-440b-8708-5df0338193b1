package config

import (
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
	"k8s.io/client-go/util/homedir"
)

// AppConfig 应用配置结构
type AppConfig struct {
	Theme       ThemeConfig       `yaml:"theme"`
	Keybindings KeybindingsConfig `yaml:"keybindings"`
	Layout      LayoutConfig      `yaml:"layout"`
	General     GeneralConfig     `yaml:"general"`
}

// ThemeConfig 主题配置
type ThemeConfig struct {
	ColorScheme string       `yaml:"color_scheme"` // catppuccin
	Variant     string       `yaml:"variant"`      // mocha, latte, frappe, macchiato
	Colors      ColorsConfig `yaml:"colors"`       // 自定义颜色配置
}

// ColorsConfig 颜色配置
type ColorsConfig struct {
	// Primary colors
	Primary   string `yaml:"primary"`   // 主色调
	Secondary string `yaml:"secondary"` // 次要色调
	Success   string `yaml:"success"`   // 成功状态色
	Warning   string `yaml:"warning"`   // 警告状态色
	Error     string `yaml:"error"`     // 错误状态色
	Info      string `yaml:"info"`      // 信息状态色

	// Neutral colors
	Background string `yaml:"background"` // 背景色
	Surface    string `yaml:"surface"`    // 表面色
	Border     string `yaml:"border"`     // 边框色
	Text       string `yaml:"text"`       // 文本色
	TextMuted  string `yaml:"text_muted"` // 次要文本色
	TextDim    string `yaml:"text_dim"`   // 暗淡文本色

	// Status colors
	StatusRunning     string `yaml:"status_running"`     // 运行状态色
	StatusPending     string `yaml:"status_pending"`     // 等待状态色
	StatusFailed      string `yaml:"status_failed"`      // 失败状态色
	StatusTerminating string `yaml:"status_terminating"` // 终止状态色
	StatusUnknown     string `yaml:"status_unknown"`     // 未知状态色

	// Selection colors
	Selected    string `yaml:"selected"`    // 选中色
	Highlighted string `yaml:"highlighted"` // 高亮色
}

// KeybindingsConfig 快捷键配置
type KeybindingsConfig struct {
	Quit         []string `yaml:"quit"`
	Refresh      []string `yaml:"refresh"`
	Navigate     []string `yaml:"navigate"`
	SelectPod    string   `yaml:"select_pod"`
	SelectSvc    string   `yaml:"select_service"`
	SelectDeploy string   `yaml:"select_deployment"`
	SelectNode   string   `yaml:"select_node"`
	SelectNS     string   `yaml:"select_namespace"`
}

// LayoutConfig 布局配置
type LayoutConfig struct {
	SidebarWidth    int  `yaml:"sidebar_width"`
	ShowIcons       bool `yaml:"show_icons"`
	ShowStatusIcons bool `yaml:"show_status_icons"`
	CompactMode     bool `yaml:"compact_mode"`
}

// GeneralConfig 通用配置
type GeneralConfig struct {
	RefreshInterval int    `yaml:"refresh_interval"` // 秒
	Language        string `yaml:"language"`         // en, zh
	LogLevel        string `yaml:"log_level"`        // debug, info, warn, error
}

// DefaultAppConfig 返回默认应用配置
func DefaultAppConfig() *AppConfig {
	theme := ThemeConfig{
		ColorScheme: "catppuccin",
		Variant:     "mocha",
		Colors:      ColorsConfig{}, // 空配置，将使用预设主题
	}
	theme.Colors = GetThemeColors(theme)

	return &AppConfig{
		Theme: theme,
		Keybindings: KeybindingsConfig{
			Quit:         []string{"q", "ctrl+c"},
			Refresh:      []string{"r", "f5"},
			Navigate:     []string{"j", "k", "up", "down"},
			SelectPod:    "1",
			SelectSvc:    "2",
			SelectDeploy: "3",
			SelectNode:   "4",
			SelectNS:     "5",
		},
		Layout: LayoutConfig{
			SidebarWidth:    20,
			ShowIcons:       true,
			ShowStatusIcons: true,
			CompactMode:     false,
		},
		General: GeneralConfig{
			RefreshInterval: 5,
			Language:        "en",
			LogLevel:        "info",
		},
	}
}

// getCatppuccinMochaColors 返回Catppuccin Mocha主题的默认颜色配置
func getCatppuccinMochaColors() ColorsConfig {
	return ColorsConfig{
		// Primary colors - Catppuccin Mocha
		Primary:   "#cba6f7", // Mauve
		Secondary: "#89b4fa", // Blue
		Success:   "#a6e3a1", // Green
		Warning:   "#f9e2af", // Yellow
		Error:     "#f38ba8", // Red
		Info:      "#89dceb", // Sky

		// Neutral colors - Catppuccin Mocha
		Background: "#1e1e2e", // Base
		Surface:    "#313244", // Surface0
		Border:     "#585b70", // Surface2
		Text:       "#cdd6f4", // Text
		TextMuted:  "#a6adc8", // Subtext0
		TextDim:    "#9399b2", // Overlay2

		// Status colors
		StatusRunning:     "#a6e3a1", // Green
		StatusPending:     "#f9e2af", // Yellow
		StatusFailed:      "#f38ba8", // Red
		StatusTerminating: "#fab387", // Peach
		StatusUnknown:     "#a6adc8", // Subtext0

		// Selection colors
		Selected:    "#45475a", // Surface1
		Highlighted: "#585b70", // Surface2
	}
}

// GetThemeColors 根据主题配置获取颜色配置
func GetThemeColors(theme ThemeConfig) ColorsConfig {
	// 如果有自定义颜色配置且不为空，优先使用自定义配置
	if !isColorsConfigEmpty(theme.Colors) {
		return theme.Colors
	}

	// 否则根据ColorScheme和Variant返回预设颜色
	if theme.ColorScheme == "catppuccin" {
		switch theme.Variant {
		case "latte":
			return getCatppuccinLatteColors()
		case "frappe":
			return getCatppuccinFrappeColors()
		case "macchiato":
			return getCatppuccinMacchiatoColors()
		case "mocha":
			fallthrough
		default:
			return getCatppuccinMochaColors()
		}
	}

	// 默认返回Mocha主题
	return getCatppuccinMochaColors()
}

// isColorsConfigEmpty 检查颜色配置是否为空
func isColorsConfigEmpty(colors ColorsConfig) bool {
	return colors.Primary == "" && colors.Background == "" && colors.Text == ""
}

// getCatppuccinLatteColors 返回Catppuccin Latte主题颜色
func getCatppuccinLatteColors() ColorsConfig {
	return ColorsConfig{
		Primary:   "#8839ef", // Mauve
		Secondary: "#1e66f5", // Blue
		Success:   "#40a02b", // Green
		Warning:   "#df8e1d", // Yellow
		Error:     "#d20f39", // Red
		Info:      "#04a5e5", // Sky

		Background: "#eff1f5", // Base
		Surface:    "#e6e9ef", // Surface0
		Border:     "#acb0be", // Surface2
		Text:       "#4c4f69", // Text
		TextMuted:  "#6c6f85", // Subtext0
		TextDim:    "#7c7f93", // Overlay2

		StatusRunning:     "#40a02b", // Green
		StatusPending:     "#df8e1d", // Yellow
		StatusFailed:      "#d20f39", // Red
		StatusTerminating: "#fe640b", // Peach
		StatusUnknown:     "#6c6f85", // Subtext0

		Selected:    "#dce0e8", // Surface1
		Highlighted: "#acb0be", // Surface2
	}
}

// getCatppuccinFrappeColors 返回Catppuccin Frappe主题颜色
func getCatppuccinFrappeColors() ColorsConfig {
	return ColorsConfig{
		Primary:   "#ca9ee6", // Mauve
		Secondary: "#8caaee", // Blue
		Success:   "#a6d189", // Green
		Warning:   "#e5c890", // Yellow
		Error:     "#e78284", // Red
		Info:      "#99d1db", // Sky

		Background: "#303446", // Base
		Surface:    "#414559", // Surface0
		Border:     "#626880", // Surface2
		Text:       "#c6d0f5", // Text
		TextMuted:  "#a5adce", // Subtext0
		TextDim:    "#949cbb", // Overlay2

		StatusRunning:     "#a6d189", // Green
		StatusPending:     "#e5c890", // Yellow
		StatusFailed:      "#e78284", // Red
		StatusTerminating: "#ef9f76", // Peach
		StatusUnknown:     "#a5adce", // Subtext0

		Selected:    "#51576d", // Surface1
		Highlighted: "#626880", // Surface2
	}
}

// getCatppuccinMacchiatoColors 返回Catppuccin Macchiato主题颜色
func getCatppuccinMacchiatoColors() ColorsConfig {
	return ColorsConfig{
		Primary:   "#c6a0f6", // Mauve
		Secondary: "#8aadf4", // Blue
		Success:   "#a6da95", // Green
		Warning:   "#eed49f", // Yellow
		Error:     "#ed8796", // Red
		Info:      "#91d7e3", // Sky

		Background: "#24273a", // Base
		Surface:    "#363a4f", // Surface0
		Border:     "#5b6078", // Surface2
		Text:       "#cad3f5", // Text
		TextMuted:  "#a5adcb", // Subtext0
		TextDim:    "#939ab7", // Overlay2

		StatusRunning:     "#a6da95", // Green
		StatusPending:     "#eed49f", // Yellow
		StatusFailed:      "#ed8796", // Red
		StatusTerminating: "#f5a97f", // Peach
		StatusUnknown:     "#a5adcb", // Subtext0

		Selected:    "#494d64", // Surface1
		Highlighted: "#5b6078", // Surface2
	}
}

// LoadAppConfig 加载应用配置
func LoadAppConfig(configPath string) (*AppConfig, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = getDefaultAppConfigPath()
	}

	// 如果配置文件不存在，创建默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		defaultConfig := DefaultAppConfig()
		if err := SaveAppConfig(defaultConfig, configPath); err != nil {
			return nil, err
		}
		return defaultConfig, nil
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	// 解析 YAML
	var config AppConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, err
	}

	// 验证配置并填充默认值
	validateAndFillDefaults(&config)

	return &config, nil
}

// SaveAppConfig 保存应用配置
func SaveAppConfig(config *AppConfig, configPath string) error {
	// 确保配置目录存在
	configDir := filepath.Dir(configPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return err
	}

	// 序列化为 YAML
	data, err := yaml.Marshal(config)
	if err != nil {
		return err
	}

	// 写入文件
	return os.WriteFile(configPath, data, 0644)
}

// getDefaultAppConfigPath 获取默认应用配置文件路径
func getDefaultAppConfigPath() string {
	if home := homedir.HomeDir(); home != "" {
		return filepath.Join(home, ".kui.yaml")
	}
	return ".kui.yaml"
}

// validateAndFillDefaults 验证配置并填充默认值
func validateAndFillDefaults(config *AppConfig) {
	defaultConfig := DefaultAppConfig()

	// 验证主题配置
	if config.Theme.ColorScheme == "" {
		config.Theme.ColorScheme = defaultConfig.Theme.ColorScheme
	}
	if config.Theme.Variant == "" {
		config.Theme.Variant = defaultConfig.Theme.Variant
	}

	// 验证颜色配置，如果为空则使用默认值
	validateColorsConfig(&config.Theme.Colors, &defaultConfig.Theme.Colors)

	// 验证快捷键配置
	if len(config.Keybindings.Quit) == 0 {
		config.Keybindings.Quit = defaultConfig.Keybindings.Quit
	}
	if len(config.Keybindings.Refresh) == 0 {
		config.Keybindings.Refresh = defaultConfig.Keybindings.Refresh
	}
	if len(config.Keybindings.Navigate) == 0 {
		config.Keybindings.Navigate = defaultConfig.Keybindings.Navigate
	}
	if config.Keybindings.SelectPod == "" {
		config.Keybindings.SelectPod = defaultConfig.Keybindings.SelectPod
	}
	if config.Keybindings.SelectSvc == "" {
		config.Keybindings.SelectSvc = defaultConfig.Keybindings.SelectSvc
	}
	if config.Keybindings.SelectDeploy == "" {
		config.Keybindings.SelectDeploy = defaultConfig.Keybindings.SelectDeploy
	}
	if config.Keybindings.SelectNode == "" {
		config.Keybindings.SelectNode = defaultConfig.Keybindings.SelectNode
	}
	if config.Keybindings.SelectNS == "" {
		config.Keybindings.SelectNS = defaultConfig.Keybindings.SelectNS
	}

	// 验证布局配置
	if config.Layout.SidebarWidth <= 0 {
		config.Layout.SidebarWidth = defaultConfig.Layout.SidebarWidth
	}

	// 验证通用配置
	if config.General.RefreshInterval <= 0 {
		config.General.RefreshInterval = defaultConfig.General.RefreshInterval
	}
	if config.General.Language == "" {
		config.General.Language = defaultConfig.General.Language
	}
	if config.General.LogLevel == "" {
		config.General.LogLevel = defaultConfig.General.LogLevel
	}
}

// GetConfigPath 获取配置文件路径
func GetConfigPath(customPath string) string {
	if customPath != "" {
		return customPath
	}
	return getDefaultAppConfigPath()
}

// validateColorsConfig 验证颜色配置并填充默认值
func validateColorsConfig(config *ColorsConfig, defaultConfig *ColorsConfig) {
	if config.Primary == "" {
		config.Primary = defaultConfig.Primary
	}
	if config.Secondary == "" {
		config.Secondary = defaultConfig.Secondary
	}
	if config.Success == "" {
		config.Success = defaultConfig.Success
	}
	if config.Warning == "" {
		config.Warning = defaultConfig.Warning
	}
	if config.Error == "" {
		config.Error = defaultConfig.Error
	}
	if config.Info == "" {
		config.Info = defaultConfig.Info
	}
	if config.Background == "" {
		config.Background = defaultConfig.Background
	}
	if config.Surface == "" {
		config.Surface = defaultConfig.Surface
	}
	if config.Border == "" {
		config.Border = defaultConfig.Border
	}
	if config.Text == "" {
		config.Text = defaultConfig.Text
	}
	if config.TextMuted == "" {
		config.TextMuted = defaultConfig.TextMuted
	}
	if config.TextDim == "" {
		config.TextDim = defaultConfig.TextDim
	}
	if config.StatusRunning == "" {
		config.StatusRunning = defaultConfig.StatusRunning
	}
	if config.StatusPending == "" {
		config.StatusPending = defaultConfig.StatusPending
	}
	if config.StatusFailed == "" {
		config.StatusFailed = defaultConfig.StatusFailed
	}
	if config.StatusTerminating == "" {
		config.StatusTerminating = defaultConfig.StatusTerminating
	}
	if config.StatusUnknown == "" {
		config.StatusUnknown = defaultConfig.StatusUnknown
	}
	if config.Selected == "" {
		config.Selected = defaultConfig.Selected
	}
	if config.Highlighted == "" {
		config.Highlighted = defaultConfig.Highlighted
	}
}
