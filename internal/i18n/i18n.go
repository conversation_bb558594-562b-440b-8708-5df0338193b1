package i18n

import (
	"fmt"
)

// Language represents supported languages
type Language string

const (
	English Language = "en"
	Chinese Language = "zh"
)

// Translator handles internationalization
type Translator struct {
	currentLang Language
	messages    map[Language]map[string]string
}

// NewTranslator creates a new translator
func NewTranslator(lang Language) *Translator {
	t := &Translator{
		currentLang: lang,
		messages:    make(map[Language]map[string]string),
	}
	t.loadMessages()
	return t
}

// SetLanguage sets the current language
func (t *Translator) SetLanguage(lang Language) {
	t.currentLang = lang
}

// T translates a message key
func (t *Translator) T(key string, args ...interface{}) string {
	if messages, exists := t.messages[t.currentLang]; exists {
		if message, exists := messages[key]; exists {
			if len(args) > 0 {
				return fmt.Sprintf(message, args...)
			}
			return message
		}
	}
	
	// Fallback to English
	if t.currentLang != English {
		if messages, exists := t.messages[English]; exists {
			if message, exists := messages[key]; exists {
				if len(args) > 0 {
					return fmt.Sprintf(message, args...)
				}
				return message
			}
		}
	}
	
	// Return key if no translation found
	return key
}

// loadMessages loads all translation messages
func (t *Translator) loadMessages() {
	t.messages[English] = map[string]string{
		// Application
		"app.title":       "KUI",
		"app.loading":     "Loading...",
		"app.error":       "Error: %s",
		"app.no_items":    "No items",
		"app.initializing": "Initializing...",
		
		// Navigation
		"nav.workloads":    "Workloads",
		"nav.pods":         "Pods",
		"nav.services":     "Services", 
		"nav.deployments":  "Deployments",
		"nav.nodes":        "Nodes",
		"nav.namespaces":   "Namespaces",
		
		// Status
		"status.running":     "Running",
		"status.pending":     "Pending",
		"status.failed":      "Failed",
		"status.terminating": "Terminating",
		"status.unknown":     "Unknown",
		
		// Actions
		"action.quit":     "Quit",
		"action.refresh":  "Refresh",
		"action.navigate": "Navigate",
		"action.switch":   "Switch",
		
		// Info
		"info.cluster":    "Cluster",
		"info.namespace":  "Namespace",
		"info.resources":  "Resources",
		"info.updated":    "Updated",
		
		// Errors
		"error.k8s_client":     "Failed to create Kubernetes client: %s",
		"error.load_config":    "Failed to load configuration: %s",
		"error.save_config":    "Failed to save configuration: %s",
		"error.get_resources":  "Failed to get resources: %s",
		"error.cluster_info":   "Failed to get cluster info: %s",
	}
	
	t.messages[Chinese] = map[string]string{
		// Application
		"app.title":       "KUI",
		"app.loading":     "加载中...",
		"app.error":       "错误: %s",
		"app.no_items":    "无项目",
		"app.initializing": "初始化中...",
		
		// Navigation
		"nav.workloads":    "工作负载",
		"nav.pods":         "Pod",
		"nav.services":     "服务",
		"nav.deployments":  "部署",
		"nav.nodes":        "节点",
		"nav.namespaces":   "命名空间",
		
		// Status
		"status.running":     "运行中",
		"status.pending":     "等待中",
		"status.failed":      "失败",
		"status.terminating": "终止中",
		"status.unknown":     "未知",
		
		// Actions
		"action.quit":     "退出",
		"action.refresh":  "刷新",
		"action.navigate": "导航",
		"action.switch":   "切换",
		
		// Info
		"info.cluster":    "集群",
		"info.namespace":  "命名空间",
		"info.resources":  "资源",
		"info.updated":    "更新时间",
		
		// Errors
		"error.k8s_client":     "创建Kubernetes客户端失败: %s",
		"error.load_config":    "加载配置失败: %s",
		"error.save_config":    "保存配置失败: %s",
		"error.get_resources":  "获取资源失败: %s",
		"error.cluster_info":   "获取集群信息失败: %s",
	}
}

// Global translator instance
var globalTranslator *Translator

// InitI18n initializes the global translator
func InitI18n(lang Language) {
	globalTranslator = NewTranslator(lang)
}

// T translates using the global translator
func T(key string, args ...interface{}) string {
	if globalTranslator == nil {
		InitI18n(English)
	}
	return globalTranslator.T(key, args...)
}

// SetLanguage sets the global language
func SetLanguage(lang Language) {
	if globalTranslator == nil {
		InitI18n(lang)
	} else {
		globalTranslator.SetLanguage(lang)
	}
}
