# kui Application Configuration Example
# This config file only contains kui application UI and behavior settings
# Use standard kubeconfig file for Kubernetes cluster configuration

# Theme configuration (simplified - removed color_scheme and variant)
theme:
  # Color configuration (uses Catppuccin Mocha by default)
  colors:
    # Primary colors
    primary: "#cba6f7"    # Primary color (Mauve)
    secondary: "#89b4fa"  # Secondary color (Blue)
    success: "#a6e3a1"    # Success status color (Green)
    warning: "#f9e2af"    # Warning status color (Yellow)
    error: "#f38ba8"      # Error status color (Red)
    info: "#89dceb"       # Info status color (Sky)
    
    # Neutral colors
    background: "#1e1e2e" # Background color (Base)
    surface: "#313244"    # Surface color (Surface0)
    border: "#585b70"     # Border color (Surface2)
    text: "#cdd6f4"       # Text color (Text)
    text_muted: "#a6adc8" # Muted text color (Subtext0)
    text_dim: "#9399b2"   # Dim text color (Overlay2)
    
    # Status colors
    status_running: "#a6e3a1"     # Running status color
    status_pending: "#f9e2af"     # Pending status color
    status_failed: "#f38ba8"      # Failed status color
    status_terminating: "#fab387" # Terminating status color
    status_unknown: "#a6adc8"     # Unknown status color
    
    # Selection colors
    selected: "#45475a"    # Selected color (Surface1)
    highlighted: "#585b70" # Highlighted color (Surface2)

# Keybindings configuration
keybindings:
  quit: ["q", "ctrl+c"]           # Quit application
  refresh: ["r", "f5"]            # Refresh resources
  navigate: ["j", "k", "up", "down"] # Navigation movement
  select_pod: "1"                 # Select Pod resources
  select_service: "2"             # Select Service resources
  select_deployment: "3"          # Select Deployment resources
  select_node: "4"                # Select Node resources
  select_namespace: "5"           # Select Namespace resources

# Layout configuration
layout:
  sidebar_width: 20      # Sidebar width
  show_icons: true       # Show icons
  show_status_icons: true # Show status icons
  compact_mode: false    # Compact mode

# General configuration
general:
  refresh_interval: 5    # Auto refresh interval (seconds)
  language: "en"         # Interface language (en, zh)
  log_level: "info"      # Log level (debug, info, warn, error)

# Notes:
# 1. Use ~/.kube/config file for Kubernetes cluster configuration
# 2. Or specify config file path via KUBECONFIG environment variable
# 3. kui will automatically read current kubectl context and namespace settings
