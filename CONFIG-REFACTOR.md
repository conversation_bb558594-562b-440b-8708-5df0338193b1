# kui 配置和UI系统重构说明

## 重构概述

本次重构完成了kui应用程序配置和UI系统的全面升级，主要包括三个方面：

### 1. 配置文件重构 ✅

**变更内容：**
- 从kui配置文件中移除了所有Kubernetes集群相关配置
- kui现在完全依赖系统的kubeconfig文件来管理Kubernetes连接
- 配置文件只包含kui应用程序自身的设置（UI主题、快捷键、布局等）

**影响：**
- 更清晰的职责分离：kui配置专注于UI，kubeconfig专注于集群连接
- 与kubectl等工具保持一致的集群配置管理方式
- 简化了配置文件结构

**使用方式：**
```bash
# Kubernetes配置（标准方式）
export KUBECONFIG=~/.kube/config
kubectl config use-context your-cluster

# kui应用配置（可选）
./kui --config ~/.kui.yaml
```

### 2. UI配色系统重构 ✅

**新功能：**
- 支持动态主题切换
- 内置Catppuccin四种变体（mocha, latte, frappe, macchiato）
- 支持自定义颜色配置
- 运行时主题应用，无需重启

**配置示例：**
```yaml
theme:
  color_scheme: "catppuccin"
  variant: "mocha"  # mocha, latte, frappe, macchiato
  colors:
    primary: "#cba6f7"
    background: "#1e1e2e"
    # ... 更多自定义颜色
```

**技术实现：**
- 新增`ThemeManager`主题管理器
- 颜色配置从硬编码改为动态加载
- 支持配置文件热更新

### 3. UI界面结构重构 ✅

**新组件库：**
- `Panel` - 通用面板组件
- `List` - 列表组件（支持导航和表格模式）
- `StatusBar` - 状态栏组件
- `Navigation` - 导航组件

**设计规范：**
- 严格按照`ui.txt`规范实现
- 统一使用`lipgloss.RoundedBorder()`边框
- 模块化设计，便于维护和扩展

**布局改进：**
- 响应式布局支持
- 可配置的侧边栏宽度
- 更好的间距和对齐

## 配置文件结构

### 新的配置文件结构
```yaml
theme:          # 主题配置
  color_scheme: string
  variant: string
  colors: object

keybindings:    # 快捷键配置
  quit: []string
  refresh: []string
  # ...

layout:         # 布局配置
  sidebar_width: int
  show_icons: bool
  # ...

general:        # 通用配置
  refresh_interval: int
  language: string
  log_level: string
```

### 移除的配置项
- `kubeconfig` - 现在使用系统kubeconfig
- `current_context` - 现在使用kubectl当前上下文
- `namespace` - 现在使用kubeconfig中的命名空间设置

## 向后兼容性

- 现有的kui使用方式保持不变
- 如果没有配置文件，会自动创建默认配置
- 旧的配置文件会被自动迁移（移除不支持的字段）

## 使用示例

### 基本使用
```bash
# 使用默认配置启动
./kui

# 使用自定义配置
./kui --config ~/.kui.yaml
```

### 主题切换
```bash
# 编辑配置文件切换主题
vim ~/.kui.yaml
# 修改 theme.variant 为 "latte", "frappe", "macchiato" 或 "mocha"
```

### 集群管理
```bash
# 使用kubectl管理集群配置
kubectl config get-contexts
kubectl config use-context production
kubectl config set-context --current --namespace=kube-system

# 然后启动kui，会自动使用当前上下文
./kui
```

## 开发者说明

### 新增文件
- `internal/tui/styles/theme.go` - 主题管理器
- `internal/tui/components/panel.go` - 面板组件
- `internal/tui/components/list.go` - 列表组件
- `internal/tui/components/statusbar.go` - 状态栏组件
- `internal/tui/components/navigation.go` - 导航组件

### 修改文件
- `internal/config/config.go` - 移除K8s配置，保留工具函数
- `internal/config/app_config.go` - 扩展颜色主题配置
- `internal/tui/views/resource_list.go` - 使用新组件系统
- `internal/k8s/client.go` - 简化客户端创建

### 技术栈
- 继续使用bubbletea + lipgloss
- 保持单一可执行文件部署
- 支持交叉编译

## 测试

```bash
# 编译测试
make build

# 运行测试
make test

# 启动应用测试
./build/kui
```

## 总结

本次重构实现了：
1. ✅ 配置职责分离，kui专注UI设置
2. ✅ 动态主题系统，支持Catppuccin配色
3. ✅ 模块化UI组件，符合设计规范
4. ✅ 保持向后兼容性和单一可执行文件部署

重构后的kui更加专业、易用和可维护。
