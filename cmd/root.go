package cmd

import (
	"fmt"
	"os"

	"github.com/liyujun-dev/kui/internal/tui"
	"github.com/spf13/cobra"
)

var (
	cfgFile   string
	version   = "dev"
	commit    = "unknown"
	buildTime = "unknown"
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:     "kui",
	Short:   "kui - a Kubernetes Management Tool",
	Long:    `葵 (kui) 是一个基于终端的 Kubernetes 资源管理工具。`,
	Version: fmt.Sprintf("%s (Build on %s from Git SHA %s)", version, buildTime, commit),
	RunE: func(cmd *cobra.Command, args []string) error {
		// 默认启动 TUI 界面
		if len(args) == 0 {
			return startTUI()
		}
		return cmd.Help()
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	// 全局标志
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件路径 (默认为 $HOME/.kui.yaml)")
}

// startTUI 启动 TUI 界面
func startTUI() error {
	app, err := tui.NewAppWithConfig(cfgFile)
	if err != nil {
		return fmt.Errorf("创建 TUI 应用失败: %w", err)
	}

	if err := app.Run(); err != nil {
		return fmt.Errorf("运行 TUI 应用失败: %w", err)
	}

	return nil
}

// GetConfigFile 返回配置文件路径
func GetConfigFile() string {
	return cfgFile
}
